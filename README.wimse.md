## Wimse documentation


## Setup Instructions

### 0. Setup the databases (if not running yet)

In the `docker-scripts` repo, run the following command to start the databases in 2 directories:
- postgres
- redis

```bash
docker compose up -d
```

In `.env`, update the database related fields with your own values.

### 1. Copy the environment file and the docker compose file

```bash
cp .env.example .env
cp docker-compose.example.yaml docker-compose.yaml
```

### 2. Configure environment variables

Open the newly created `.env` file and update the following fields with your own values:

* `SECRET_KEY_BASE` – used to secure your application. Generate a strong, random key.
* `POSTGRES_PASSWORD` – set the password for your PostgreSQL database.
* `REDIS_PASSWORD` – set the password for your Redis database.

In the `docker-compose.yaml` file, update the `POSTGRES_PASSWORD` field with the same value you set in the `.env` file.

### 3. Grant execution permissions to the entrypoint scripts

```bash
chmod +x docker/entrypoints/*.sh
chmod 755 docker/entrypoints/helpers/pg_database_url.rb
```



### 4. Build the base Docker image

Once the environment variables are set, build the base image using Docker Compose:

```bash
docker compose build base
```

### 4. Build the other images

```bash
docker compose build
```

### 4. Init the database

```bash
docker compose run --rm rails bundle exec rails db:chatwoot_prepare
```

## Assets Compilation

RAILS_ENV can be set to `production` or `development`

```bash
docker compose exec -e NODE_OPTIONS="--max_old_space_size=4096" -e RAILS_ENV=development rails bundle exec rails assets:precompile
```
