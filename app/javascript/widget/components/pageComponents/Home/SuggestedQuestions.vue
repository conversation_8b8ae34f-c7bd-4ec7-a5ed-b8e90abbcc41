<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';

const store = useStore();

// Dummy suggested questions data
const suggestedQuestions = [
  {
    id: 1,
    text: 'How can I track my order?',
  },
  {
    id: 2,
    text: 'What are your business hours?',
  },
  {
    id: 3,
    text: 'How do I return an item?',
  },
  {
    id: 4,
    text: 'Do you offer technical support?',
  },
  {
    id: 5,
    text: 'What payment methods do you accept?',
  }
];

const widgetColor = computed(() => store.getters['appConfig/getWidgetColor']);

const handleQuestionClick = (question) => {
  // For now, just emit the event - integration will be handled later
  console.log('Question clicked:', question.text);
  // TODO: Integrate with conversation start and pre-fill message
};
</script>

<template>
  <div class="suggested-questions mb-4">
    <h3 class="text-sm font-medium text-slate-700 mb-3">
      {{ $t('SUGGESTED_QUESTIONS.TITLE') }}
    </h3>

    <div class="space-y-2">
      <button
        v-for="question in suggestedQuestions"
        :key="question.id"
        class="w-full text-left p-3 rounded-lg border border-slate-200 hover:border-slate-300 transition-colors duration-200 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-opacity-50"
        :class="`focus:ring-[${widgetColor}]`"
        @click="handleQuestionClick(question)"
      >
        <div class="flex items-start gap-3">
          <span class="text-sm text-slate-600 leading-relaxed">{{ question.text }}</span>
        </div>
      </button>
    </div>

    <p class="text-xs text-slate-500 mt-3 text-center">
      {{ $t('SUGGESTED_QUESTIONS.SUBTITLE') }}
    </p>
  </div>
</template>
