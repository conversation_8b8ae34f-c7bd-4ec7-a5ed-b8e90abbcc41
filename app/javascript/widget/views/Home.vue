<script>
import CustomButton from 'shared/components/Button.vue';
import TeamAvailability from 'widget/components/TeamAvailability.vue';
import ConversationsList from 'widget/components/ConversationsList.vue';
import Spinner from 'shared/components/Spinner.vue';
import { mapGetters, mapActions } from 'vuex';
import routerMixin from 'widget/mixins/routerMixin';
import configMixin from 'widget/mixins/configMixin';
import { getContrastingTextColor } from '@chatwoot/utils';
import { IFrameHelper } from '../helpers/utils';
import { CHATWOOT_ON_START_CONVERSATION } from '../constants/sdkEvents';
import ArticleContainer from '../components/pageComponents/Home/Article/ArticleContainer.vue';
import SuggestedQuestions from '../components/pageComponents/Home/SuggestedQuestions.vue';
export default {
  name: 'Home',
  components: {
    ArticleContainer,
    SuggestedQuestions,
    TeamAvailability,
    CustomButton,
    ConversationsList,
    Spinner
  },
  mixins: [configMixin, routerMixin],
  data() {
    return {
      isStartingNewConversation: false,
    };
  },
  computed: {
    ...mapGetters({
      availableAgents: 'agent/availableAgents',
      conversationSize: 'conversation/getConversationSize',
      unreadMessageCount: 'conversation/getUnreadMessageCount',
      widgetColor: 'appConfig/getWidgetColor',
    }),


    textColor() {
      return getContrastingTextColor(this.widgetColor);
    },
  },
  methods: {
    ...mapActions('conversation', [
      'clearConversations',
    ]),
    ...mapActions('conversationAttributes', [
      'clearConversationAttributes',
    ]),
    startConversation() {
      if (this.preChatFormEnabled && !this.conversationSize) {
        return this.replaceRoute('prechat-form');
      }
      return this.replaceRoute('messages');
    },

    async startNewConversation() {
      this.isStartingNewConversation = true;
      try {
        this.clearConversations();
        this.clearConversationAttributes();
        this.replaceRoute('messages');
        IFrameHelper.sendMessage({
          event: 'onEvent',
          eventIdentifier: CHATWOOT_ON_START_CONVERSATION,
          data: { hasConversation: true },
        });
      } finally {
        // Add a small delay to show the loading state
        setTimeout(() => {
          this.isStartingNewConversation = false;
        }, 300);
      }
    },

    selectConversation(conversation) {
      // Load the selected conversation and navigate to messages
      this.$store.commit('conversation/setConversationListLoading', true);
      this.$store.dispatch('conversation/loadConversation', conversation.id);
      this.replaceRoute('messages');
    },
  },
};
</script>

<template>
  <div class="z-50 flex flex-col justify-between flex-1 w-full p-4 ">
    <!-- Conversations List -->
    <div class="conversations-section mb-4 max-h-100 overflow-hidden">
      <!-- Suggested Questions -->
      <SuggestedQuestions />
      <ConversationsList @select-conversation="selectConversation" />
    </div>



    <CustomButton
      class="font-medium"
      block
      :bg-color="widgetColor"
      :text-color="textColor"
      :disabled="isStartingNewConversation"
      @click="startNewConversation"
    >
      <div class="flex items-center justify-center gap-2">
        <Spinner v-if="isStartingNewConversation" size="small" />
        <span>{{ $t('START_NEW_CONVERSATION') }}</span>
      </div>
    </CustomButton>

  </div>
</template>
